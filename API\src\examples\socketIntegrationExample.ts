/**
 * Example file showing how to integrate WebSocket functionality into existing controllers
 * This file demonstrates best practices for adding real-time features to your API endpoints
 */

import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { SocketUtils } from "../utils/socketUtils";
import { Logger } from "../services/loggerService";

const prisma = new PrismaClient();

// Example: Enhanced Event Controller with WebSocket integration
export class EventControllerExample {
  // Create event with real-time notifications
  static async createEvent(req: Request, res: Response) {
    try {
      const { title, description, start_time, end_time, location, is_public } = req.body;
      const authorId = req.user?.id;
      const tenantId = req.user?.tenant_id;

      if (!authorId || !tenantId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      // Create event in database
      const event = await prisma.event.create({
        data: {
          title,
          description,
          start_time: new Date(start_time),
          end_time: end_time ? new Date(end_time) : null,
          location,
          is_public: is_public ?? true,
          author_id: authorId,
          tenant_id: tenantId,
        },
        include: {
          author: { select: { full_name: true } },
        },
      });

      // Send real-time notification to all users in tenant
      await SocketUtils.triggerEventCreated(event.id, tenantId, authorId);

      // Send activity update
      await SocketUtils.sendActivityUpdate(tenantId, "event_created", authorId, {
        eventId: event.id,
        eventTitle: event.title,
      });

      res.status(201).json({
        success: true,
        data: event,
        message: "Event created successfully",
      });
    } catch (error) {
      Logger.error("Error creating event:", error);
      res.status(500).json({ error: "Failed to create event" });
    }
  }

  // Update event with real-time notifications
  static async updateEvent(req: Request, res: Response) {
    try {
      const eventId = parseInt(req.params.id);
      const { title, description, start_time, end_time, location, is_public } = req.body;
      const userId = req.user?.id;
      const tenantId = req.user?.tenant_id;

      if (!userId || !tenantId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      // Check if event exists and user has permission to update
      const existingEvent = await prisma.event.findFirst({
        where: {
          id: eventId,
          tenant_id: tenantId,
          author_id: userId, // Only author can update
        },
      });

      if (!existingEvent) {
        return res.status(404).json({ error: "Event not found or unauthorized" });
      }

      // Update event
      const updatedEvent = await prisma.event.update({
        where: { id: eventId },
        data: {
          title,
          description,
          start_time: start_time ? new Date(start_time) : undefined,
          end_time: end_time ? new Date(end_time) : undefined,
          location,
          is_public,
        },
        include: {
          author: { select: { full_name: true } },
        },
      });

      // Send real-time update notification
      await SocketUtils.triggerEventUpdated(eventId, tenantId, userId);

      res.json({
        success: true,
        data: updatedEvent,
        message: "Event updated successfully",
      });
    } catch (error) {
      Logger.error("Error updating event:", error);
      res.status(500).json({ error: "Failed to update event" });
    }
  }

  // Delete event with real-time notifications
  static async deleteEvent(req: Request, res: Response) {
    try {
      const eventId = parseInt(req.params.id);
      const userId = req.user?.id;
      const tenantId = req.user?.tenant_id;

      if (!userId || !tenantId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      // Check if event exists and user has permission to delete
      const existingEvent = await prisma.event.findFirst({
        where: {
          id: eventId,
          tenant_id: tenantId,
          author_id: userId, // Only author can delete
        },
      });

      if (!existingEvent) {
        return res.status(404).json({ error: "Event not found or unauthorized" });
      }

      // Delete event
      await prisma.event.delete({
        where: { id: eventId },
      });

      // Send real-time deletion notification
      await SocketUtils.triggerEventDeleted(eventId, tenantId, userId);

      res.json({
        success: true,
        message: "Event deleted successfully",
      });
    } catch (error) {
      Logger.error("Error deleting event:", error);
      res.status(500).json({ error: "Failed to delete event" });
    }
  }
}

// Example: Enhanced Follow Controller with WebSocket integration
export class FollowControllerExample {
  // Send follow request with real-time notification
  static async sendFollowRequest(req: Request, res: Response) {
    try {
      const { followingId } = req.body;
      const followerId = req.user?.id;
      const tenantId = req.user?.tenant_id;

      if (!followerId || !tenantId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      if (followerId === followingId) {
        return res.status(400).json({ error: "Cannot follow yourself" });
      }

      // Check if target user exists
      const targetUser = await prisma.user.findFirst({
        where: {
          id: followingId,
          tenant_id: tenantId,
          account_status: "APPROVED",
        },
      });

      if (!targetUser) {
        return res.status(404).json({ error: "User not found" });
      }

      // Check if follow request already exists
      const existingFollow = await prisma.follow.findUnique({
        where: {
          follower_id_following_id: {
            follower_id: followerId,
            following_id: followingId,
          },
        },
      });

      if (existingFollow) {
        return res.status(400).json({ error: "Follow request already exists" });
      }

      // Create follow request
      const followRequest = await prisma.follow.create({
        data: {
          follower_id: followerId,
          following_id: followingId,
          tenant_id: tenantId,
          status: "PENDING",
        },
      });

      // Send real-time notification
      await SocketUtils.triggerFollowRequest(followerId, followingId, tenantId);

      res.status(201).json({
        success: true,
        data: followRequest,
        message: "Follow request sent successfully",
      });
    } catch (error) {
      Logger.error("Error sending follow request:", error);
      res.status(500).json({ error: "Failed to send follow request" });
    }
  }

  // Respond to follow request with real-time notification
  static async respondToFollowRequest(req: Request, res: Response) {
    try {
      const { followerId, action } = req.body; // action: 'accept' or 'reject'
      const followingId = req.user?.id;
      const tenantId = req.user?.tenant_id;

      if (!followingId || !tenantId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      if (!["accept", "reject"].includes(action)) {
        return res.status(400).json({ error: "Invalid action" });
      }

      // Find the follow request
      const followRequest = await prisma.follow.findUnique({
        where: {
          follower_id_following_id: {
            follower_id: followerId,
            following_id: followingId,
          },
        },
      });

      if (!followRequest || followRequest.status !== "PENDING") {
        return res.status(404).json({ error: "Follow request not found or already processed" });
      }

      // Update follow request status
      const newStatus = action === "accept" ? "ACCEPTED" : "REJECTED";
      const updatedFollow = await prisma.follow.update({
        where: {
          follower_id_following_id: {
            follower_id: followerId,
            following_id: followingId,
          },
        },
        data: { status: newStatus },
      });

      // Send real-time notification
      if (action === "accept") {
        await SocketUtils.triggerFollowAccepted(followerId, followingId, tenantId);
      }

      res.json({
        success: true,
        data: updatedFollow,
        message: `Follow request ${action}ed successfully`,
      });
    } catch (error) {
      Logger.error("Error responding to follow request:", error);
      res.status(500).json({ error: "Failed to respond to follow request" });
    }
  }
}

// Example: Enhanced Post Controller with WebSocket integration
export class PostControllerExample {
  // Like a post with real-time notification
  static async likePost(req: Request, res: Response) {
    try {
      const postId = parseInt(req.params.id);
      const userId = req.user?.id;
      const tenantId = req.user?.tenant_id;

      if (!userId || !tenantId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      // Find the post
      const post = await prisma.generalPost.findFirst({
        where: {
          id: postId,
          tenant_id: tenantId,
        },
        select: {
          id: true,
          author_id: true,
          title: true,
        },
      });

      if (!post) {
        return res.status(404).json({ error: "Post not found" });
      }

      // Here you would typically create a like record in a likes table
      // For this example, we'll just trigger the notification

      // Send real-time notification to post author (if not liking own post)
      if (post.author_id !== userId) {
        await SocketUtils.triggerPostLiked(postId, tenantId, userId, post.author_id);
      }

      res.json({
        success: true,
        message: "Post liked successfully",
      });
    } catch (error) {
      Logger.error("Error liking post:", error);
      res.status(500).json({ error: "Failed to like post" });
    }
  }
}

// Example: How to use WebSocket utilities in any controller
export class GeneralControllerExample {
  // Send custom notification
  static async sendCustomNotification(req: Request, res: Response) {
    try {
      const { targetUserId, title, message, type } = req.body;
      const currentUserId = req.user?.id;
      const tenantId = req.user?.tenant_id;

      if (!currentUserId || !tenantId) {
        return res.status(401).json({ error: "Unauthorized" });
      }

      await SocketUtils.sendCustomNotification(
        targetUserId,
        tenantId,
        type,
        title,
        message,
        { sentBy: currentUserId }
      );

      res.json({
        success: true,
        message: "Notification sent successfully",
      });
    } catch (error) {
      Logger.error("Error sending custom notification:", error);
      res.status(500).json({ error: "Failed to send notification" });
    }
  }

  // Check online status
  static async checkOnlineStatus(req: Request, res: Response) {
    try {
      const targetUserId = parseInt(req.params.userId);
      const isOnline = SocketUtils.isUserOnline(targetUserId);

      res.json({
        success: true,
        data: { userId: targetUserId, isOnline },
      });
    } catch (error) {
      Logger.error("Error checking online status:", error);
      res.status(500).json({ error: "Failed to check online status" });
    }
  }
}
