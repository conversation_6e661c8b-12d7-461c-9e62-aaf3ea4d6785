import { Server as SocketIOServer } from "socket.io";
import { PrismaClient } from "@prisma/client";
import { SOCKET_EVENTS, SOCKET_ROOMS } from "../config/socket";
import { socketAuthMiddleware, emitSocketError, emitValidationError, socketRateLimit } from "../middleware/socketAuth";
import { socketService } from "../services/socketService";
import {
  AuthenticatedSocket,
  ChatMessage,
  TypingIndicator,
  UserStatus,
  RoomData,
  SocketNotification,
  NotificationType,
} from "../types/socket";
import { Logger } from "../services/loggerService";

const prisma = new PrismaClient();

// Initialize all socket event handlers
export const initializeSocketHandlers = (io: SocketIOServer) => {
  // Initialize socket service
  socketService.initialize(io);

  // Authentication middleware
  io.use(socketAuthMiddleware);

  // Connection handler
  io.on(SOCKET_EVENTS.CONNECTION, (socket: AuthenticatedSocket) => {
    Logger.info(`Socket connected: ${socket.id} for user ${socket.userId}`);

    // Handle user connection
    socketService.handleUserConnection(socket);

    // Authentication event
    socket.on(SOCKET_EVENTS.AUTHENTICATE, async (data) => {
      try {
        socket.emit(SOCKET_EVENTS.AUTHENTICATED, {
          userId: socket.userId,
          tenantId: socket.tenantId,
          timestamp: new Date(),
        });
      } catch (error) {
        emitSocketError(socket, {
          code: "AUTH_ERROR",
          message: "Authentication failed",
          details: error,
        });
      }
    });

    // User presence handlers
    setupPresenceHandlers(socket);

    // Messaging handlers
    setupMessagingHandlers(socket);

    // Notification handlers
    setupNotificationHandlers(socket);

    // Room management handlers
    setupRoomHandlers(socket);

    // Follow/Connection handlers
    setupFollowHandlers(socket);

    // Real-time update handlers
    setupUpdateHandlers(socket);

    // Disconnection handler
    socket.on(SOCKET_EVENTS.DISCONNECT, (reason) => {
      Logger.info(`Socket disconnected: ${socket.id} for user ${socket.userId}, reason: ${reason}`);
      socketService.handleUserDisconnection(socket);
    });

    // Error handler
    socket.on("error", (error) => {
      Logger.error(`Socket error for ${socket.id}:`, error);
    });
  });

  Logger.info("Socket handlers initialized");
};

// User presence event handlers
const setupPresenceHandlers = (socket: AuthenticatedSocket) => {
  // Get online users
  socket.on(SOCKET_EVENTS.GET_ONLINE_USERS, async () => {
    try {
      if (!socket.tenantId) return;

      const onlineUsers = socketService.getOnlineUsersForTenant(socket.tenantId);
      socket.emit(SOCKET_EVENTS.ONLINE_USERS_LIST, onlineUsers);
    } catch (error) {
      emitSocketError(socket, {
        code: "PRESENCE_ERROR",
        message: "Failed to get online users",
        details: error,
      });
    }
  });

  // Update user status
  socket.on(SOCKET_EVENTS.USER_STATUS_CHANGE, async (data: { status: UserStatus }) => {
    try {
      if (!socket.userId || !Object.values(UserStatus).includes(data.status)) {
        return emitValidationError(socket, "Invalid status value");
      }

      await socketService.updateUserStatus(socket.userId, data.status);
    } catch (error) {
      emitSocketError(socket, {
        code: "STATUS_UPDATE_ERROR",
        message: "Failed to update user status",
        details: error,
      });
    }
  });
};

// Messaging event handlers
const setupMessagingHandlers = (socket: AuthenticatedSocket) => {
  // Send private message
  socket.on(SOCKET_EVENTS.SEND_MESSAGE, socketRateLimit(50, 60000), async (data: ChatMessage) => {
    try {
      if (!socket.userId || !data.receiverId || !data.content?.trim()) {
        return emitValidationError(socket, "Invalid message data");
      }

      // Verify receiver exists and is in same tenant
      const receiver = await prisma.user.findFirst({
        where: {
          id: data.receiverId,
          tenant_id: socket.tenantId,
          account_status: "APPROVED",
        },
      });

      if (!receiver) {
        return emitValidationError(socket, "Receiver not found or not accessible");
      }

      const message: ChatMessage = {
        id: `msg_${Date.now()}_${socket.userId}`,
        senderId: socket.userId,
        receiverId: data.receiverId,
        content: data.content.trim(),
        messageType: data.messageType || ("text" as any),
        timestamp: new Date(),
        isRead: false,
        isDelivered: false,
      };

      await socketService.sendPrivateMessage(socket.userId, data.receiverId, message);
    } catch (error) {
      emitSocketError(socket, {
        code: "MESSAGE_SEND_ERROR",
        message: "Failed to send message",
        details: error,
      });
    }
  });

  // Typing indicators
  socket.on(SOCKET_EVENTS.TYPING_START, (data: { receiverId?: number; roomId?: string }) => {
    try {
      if (!socket.userId || (!data.receiverId && !data.roomId)) {
        return emitValidationError(socket, "Invalid typing indicator data");
      }

      const indicator: TypingIndicator = {
        userId: socket.userId,
        userName: "User", // This would be fetched from user data
        receiverId: data.receiverId,
        roomId: data.roomId,
        isTyping: true,
      };

      socketService.handleTypingIndicator(indicator);
    } catch (error) {
      emitSocketError(socket, {
        code: "TYPING_ERROR",
        message: "Failed to handle typing indicator",
        details: error,
      });
    }
  });

  socket.on(SOCKET_EVENTS.TYPING_STOP, (data: { receiverId?: number; roomId?: string }) => {
    try {
      if (!socket.userId || (!data.receiverId && !data.roomId)) {
        return emitValidationError(socket, "Invalid typing indicator data");
      }

      const indicator: TypingIndicator = {
        userId: socket.userId,
        userName: "User",
        receiverId: data.receiverId,
        roomId: data.roomId,
        isTyping: false,
      };

      socketService.handleTypingIndicator(indicator);
    } catch (error) {
      emitSocketError(socket, {
        code: "TYPING_ERROR",
        message: "Failed to handle typing indicator",
        details: error,
      });
    }
  });

  // Message read receipt
  socket.on(SOCKET_EVENTS.MESSAGE_READ, async (data: { messageId: string; senderId: number }) => {
    try {
      if (!socket.userId || !data.messageId || !data.senderId) {
        return emitValidationError(socket, "Invalid message read data");
      }

      // Notify sender that message was read
      socket.to(SOCKET_ROOMS.USER(data.senderId)).emit(SOCKET_EVENTS.MESSAGE_READ, {
        messageId: data.messageId,
        readBy: socket.userId,
        readAt: new Date(),
      });
    } catch (error) {
      emitSocketError(socket, {
        code: "MESSAGE_READ_ERROR",
        message: "Failed to mark message as read",
        details: error,
      });
    }
  });
};

// Notification event handlers
const setupNotificationHandlers = (socket: AuthenticatedSocket) => {
  // Mark notification as read
  socket.on(SOCKET_EVENTS.NOTIFICATION_READ, async (data: { notificationId: string }) => {
    try {
      if (!socket.userId || !data.notificationId) {
        return emitValidationError(socket, "Invalid notification data");
      }

      // Here you would update the notification in the database
      // For now, just emit confirmation
      socket.emit(SOCKET_EVENTS.NOTIFICATION_READ, {
        notificationId: data.notificationId,
        readAt: new Date(),
      });
    } catch (error) {
      emitSocketError(socket, {
        code: "NOTIFICATION_ERROR",
        message: "Failed to mark notification as read",
        details: error,
      });
    }
  });
};

// Room management handlers
const setupRoomHandlers = (socket: AuthenticatedSocket) => {
  // Join room
  socket.on(SOCKET_EVENTS.JOIN_ROOM, async (data: RoomData) => {
    try {
      if (!data.roomId || !data.roomType) {
        return emitValidationError(socket, "Invalid room data");
      }

      await socketService.joinRoom(socket, data.roomId, data.roomType);

      socket.emit(SOCKET_EVENTS.JOIN_ROOM, {
        roomId: data.roomId,
        roomType: data.roomType,
        joinedAt: new Date(),
      });
    } catch (error) {
      emitSocketError(socket, {
        code: "ROOM_JOIN_ERROR",
        message: "Failed to join room",
        details: error,
      });
    }
  });

  // Leave room
  socket.on(SOCKET_EVENTS.LEAVE_ROOM, async (data: RoomData) => {
    try {
      if (!data.roomId || !data.roomType) {
        return emitValidationError(socket, "Invalid room data");
      }

      await socketService.leaveRoom(socket, data.roomId, data.roomType);

      socket.emit(SOCKET_EVENTS.LEAVE_ROOM, {
        roomId: data.roomId,
        roomType: data.roomType,
        leftAt: new Date(),
      });
    } catch (error) {
      emitSocketError(socket, {
        code: "ROOM_LEAVE_ERROR",
        message: "Failed to leave room",
        details: error,
      });
    }
  });
};

// Follow/Connection handlers
const setupFollowHandlers = (socket: AuthenticatedSocket) => {
  // Send follow request
  socket.on(SOCKET_EVENTS.FOLLOW_REQUEST, async (data: { followingId: number }) => {
    try {
      if (!socket.userId || !data.followingId || socket.userId === data.followingId) {
        return emitValidationError(socket, "Invalid follow request data");
      }

      // Verify target user exists and is in same tenant
      const targetUser = await prisma.user.findFirst({
        where: {
          id: data.followingId,
          tenant_id: socket.tenantId!,
          account_status: "APPROVED",
        },
        select: { id: true, full_name: true },
      });

      if (!targetUser) {
        return emitValidationError(socket, "Target user not found");
      }

      // Check if follow relationship already exists
      const existingFollow = await prisma.follow.findUnique({
        where: {
          follower_id_following_id: {
            follower_id: socket.userId,
            following_id: data.followingId,
          },
        },
      });

      if (existingFollow) {
        return emitValidationError(socket, "Follow request already exists");
      }

      // Create follow request
      const followRequest = await prisma.follow.create({
        data: {
          follower_id: socket.userId,
          following_id: data.followingId,
          tenant_id: socket.tenantId!,
          status: "PENDING",
        },
        include: {
          follower: { select: { full_name: true } },
        },
      });

      // Send notification to target user
      await socketService.handleFollowRequest({
        id: followRequest.follower_id,
        followerId: socket.userId,
        followingId: data.followingId,
        tenantId: socket.tenantId!,
        status: "PENDING" as any,
        followerName: followRequest.follower.full_name,
        createdAt: followRequest.created_at,
      });

      socket.emit(SOCKET_EVENTS.FOLLOW_REQUEST, {
        success: true,
        followingId: data.followingId,
        status: "PENDING",
      });
    } catch (error) {
      emitSocketError(socket, {
        code: "FOLLOW_REQUEST_ERROR",
        message: "Failed to send follow request",
        details: error,
      });
    }
  });

  // Respond to follow request
  socket.on(SOCKET_EVENTS.FOLLOW_ACCEPTED, async (data: { followerId: number; action: "accept" | "reject" }) => {
    try {
      if (!socket.userId || !data.followerId || !["accept", "reject"].includes(data.action)) {
        return emitValidationError(socket, "Invalid follow response data");
      }

      const followRequest = await prisma.follow.findUnique({
        where: {
          follower_id_following_id: {
            follower_id: data.followerId,
            following_id: socket.userId,
          },
        },
        include: {
          following: { select: { full_name: true } },
        },
      });

      if (!followRequest || followRequest.status !== "PENDING") {
        return emitValidationError(socket, "Follow request not found or already processed");
      }

      const newStatus = data.action === "accept" ? "ACCEPTED" : "REJECTED";

      await prisma.follow.update({
        where: {
          follower_id_following_id: {
            follower_id: data.followerId,
            following_id: socket.userId,
          },
        },
        data: { status: newStatus },
      });

      // Notify the follower
      const notification: SocketNotification = {
        userId: data.followerId,
        tenantId: socket.tenantId!,
        type: data.action === "accept" ? NotificationType.FOLLOW_ACCEPTED : NotificationType.FOLLOW_ACCEPTED,
        title: data.action === "accept" ? "Follow Request Accepted" : "Follow Request Rejected",
        message: `${followRequest.following.full_name} ${data.action}ed your follow request`,
        createdAt: new Date(),
      };

      await socketService.sendNotificationToUser(data.followerId, notification);

      socket.emit(data.action === "accept" ? SOCKET_EVENTS.FOLLOW_ACCEPTED : SOCKET_EVENTS.FOLLOW_REJECTED, {
        followerId: data.followerId,
        action: data.action,
        timestamp: new Date(),
      });
    } catch (error) {
      emitSocketError(socket, {
        code: "FOLLOW_RESPONSE_ERROR",
        message: "Failed to respond to follow request",
        details: error,
      });
    }
  });
};

// Real-time update handlers
const setupUpdateHandlers = (socket: AuthenticatedSocket) => {
  // These handlers would typically be called from other parts of the application
  // when events, jobs, or posts are created/updated/deleted

  // For now, we'll set up listeners that can be triggered by the application
  socket.on("internal:event_update", (eventUpdate) => {
    socketService.handleEventUpdate(eventUpdate);
  });

  socket.on("internal:job_update", (jobUpdate) => {
    socketService.handleJobUpdate(jobUpdate);
  });

  socket.on("internal:post_update", (postUpdate) => {
    socketService.handlePostUpdate(postUpdate);
  });
};
