generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id         Int      @id @default(autoincrement())
  name       String   @db.VarChar(255)
  subdomain  String   @unique @db.VarChar(50)
  logo_url   String?  @db.Var<PERSON>har(255)
  is_active  Boolean  @default(true)
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(now()) @updatedAt @db.Timestamp(0)

  users         User[]
  courses       Course[]
  user_profiles UserProfile[]
  general_posts GeneralPost[]
  jobs          Job[]

  @@map("tenants")
}

model User {
  id             Int        @id @default(autoincrement())
  tenant_id      Int
  full_name      String     @db.VarChar(255)
  email          String     @db.VarChar(255)
  password_hash  String     @db.VarChar(255)
  mobile_number  String?    @db.VarChar(20)
  usn            String     @db.VarChar(50)
  role           UserRole
  account_status UserStatus @default(PENDING)
  created_at     DateTime   @default(now()) @db.Timestamp(0)
  updated_at     DateTime   @default(now()) @updatedAt @db.Timestamp(0)

  tenant        Tenant        @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  profile       UserProfile?
  general_posts GeneralPost[]
  jobs          Job[]

  @@unique([tenant_id, email], name: "idx_tenant_email")
  @@unique([tenant_id, usn], name: "idx_tenant_usn")
  @@map("users")
}

model Course {
  id          Int      @id @default(autoincrement())
  tenant_id   Int
  course_name String   @db.VarChar(255)
  created_at  DateTime @default(now()) @db.Timestamp(0)

  tenant   Tenant        @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  profiles UserProfile[]

  @@map("courses")
}

model UserProfile {
  user_id          Int      @id
  tenant_id        Int
  course_id        Int?
  batch_year       Int?
  current_location String?  @db.VarChar(255)
  linkedin_url     String?  @db.VarChar(255)
  company          String?  @db.VarChar(255)
  job_title        String?  @db.VarChar(255)
  privacy_settings Json?
  updated_at       DateTime @default(now()) @updatedAt @db.Timestamp(0)

  user   User    @relation(fields: [user_id], references: [id], onDelete: Cascade)
  tenant Tenant  @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  course Course? @relation(fields: [course_id], references: [id], onDelete: SetNull)

  @@map("user_profiles")
}

model GeneralPost {
  id         Int      @id @default(autoincrement())
  tenant_id  Int
  author_id  Int
  title      String?  @db.VarChar(255)
  content    String   @db.Text
  is_public  Boolean  @default(true)
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(now()) @updatedAt @db.Timestamp(0)

  tenant Tenant @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  author User   @relation(fields: [author_id], references: [id], onDelete: Cascade)

  @@map("general_posts")
}

model Job {
  id                  Int      @id @default(autoincrement())
  tenant_id           Int
  author_id           Int
  title               String   @db.VarChar(255)
  company_name        String   @db.VarChar(255)
  location            String?  @db.VarChar(255)
  description         String   @db.Text
  apply_link_or_email String   @db.VarChar(255)
  job_type            JobType?
  work_mode           WorkMode
  experience_level    String?  @db.VarChar(50)
  is_public           Boolean  @default(true)
  created_at          DateTime @default(now()) @db.Timestamp(0)
  updated_at          DateTime @default(now()) @updatedAt @db.Timestamp(0)

  tenant Tenant @relation(fields: [tenant_id], references: [id], onDelete: Cascade)
  author User   @relation(fields: [author_id], references: [id], onDelete: Cascade)

  @@map("jobs")
}

enum UserRole {
  STUDENT
  ALUMNUS
  TENANT_ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  PENDING
  APPROVED
  REJECTED
  DEACTIVATED
}

enum JobType {
  FULL_TIME
  PART_TIME
  INTERNSHIP
  CONTRACT
}

enum WorkMode {
  Remote
  Hybrid
  Onsite
}
